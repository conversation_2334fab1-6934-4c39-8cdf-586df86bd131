<?php

// Simple test to verify the logic for removing sections with no data

class MockCollegeService {
    
    /**
     * Mock version of formatTopCollegesForTemplate method
     */
    public function formatTopCollegesForTemplate($results, $category)
    {
        $replacements = [];

        // Check if we have any valid college data
        if (empty($results) || empty($results['colleges']) || count($results['colleges']) === 0) {
            // Return special marker to indicate no data available for this category
            $replacements['_NO_DATA_' . strtoupper($category)] = true;
            return $replacements;
        }

        // If we have data, create normal replacements
        for ($i = 1; $i <= 5; $i++) {
            $college = isset($results['colleges'][$i - 1]) ? $results['colleges'][$i - 1] : null;

            if ($college) {
                $collegeName = !empty($college['display_name']) ? $college['display_name'] : $college['name'];
                $feeRange = 'Not Available';
                $rank = !empty($college['rank']) ? $college['rank'] : 'Not Ranked';

                $replacements['{College_' . ucfirst($category) . '_Name_' . $i . '}'] = $collegeName;
                $replacements['{Fees_' . ucfirst($category) . '_Range_' . $i . '}'] = $feeRange;
                $replacements['{Latest_' . ucfirst($category) . '_NIRF_Rank_' . $i . '}'] = $rank;
            } else {
                $replacements['{College_' . ucfirst($category) . '_Name_' . $i . '}'] = 'Not Available';
                $replacements['{Fees_' . ucfirst($category) . '_Range_' . $i . '}'] = 'Not Available';
                $replacements['{Latest_' . ucfirst($category) . '_NIRF_Rank_' . $i . '}'] = 'Not Available';
            }
        }

        return $replacements;
    }

    /**
     * Mock version of removeSectionsWithNoData method
     */
    public function removeSectionsWithNoData($templateContent, $categoryReplacements)
    {
        $categories = ['engineering', 'management', 'commerce', 'medical', 'law'];
        
        foreach ($categories as $category) {
            $categoryKey = '_NO_DATA_' . strtoupper($category);
            
            // Check if this category has no data
            if (isset($categoryReplacements[$category][$categoryKey])) {
                // Define patterns to match sections for this category
                $patterns = [
                    // Pattern for sections with headings like "Top Engineering Colleges" followed by table
                    '/\s*<h[1-6][^>]*>.*?' . ucfirst($category) . '.*?<\/h[1-6]>\s*.*?<table[^>]*>.*?<\/table>\s*/is',
                    
                    // Pattern for sections with just table containing category placeholders
                    '/\s*<table[^>]*>.*?{College_' . ucfirst($category) . '_Name_1}.*?<\/table>\s*/is',
                    
                    // Pattern for div sections containing category data
                    '/\s*<div[^>]*>.*?{College_' . ucfirst($category) . '_Name_1}.*?<\/div>\s*/is',
                    
                    // Pattern for any section containing category placeholders (more general)
                    '/\s*<[^>]+>.*?{[^}]*' . ucfirst($category) . '[^}]*}.*?<\/[^>]+>\s*/is'
                ];
                
                foreach ($patterns as $pattern) {
                    $templateContent = preg_replace($pattern, '', $templateContent);
                }
                
                // Also remove any standalone category placeholders that might remain
                $templateContent = preg_replace('/\s*{[^}]*' . ucfirst($category) . '[^}]*}\s*/', '', $templateContent);
            }
        }
        
        // Clean up any extra whitespace or empty lines
        $templateContent = preg_replace('/\n\s*\n\s*\n/', "\n\n", $templateContent);
        
        return $templateContent;
    }
}

// Test the functionality
$service = new MockCollegeService();

// Test case 1: No data for commerce
$testTemplate = '
<h2>Top Engineering Colleges in India</h2>
<table>
<tr><th>College Name</th><th>Fees</th><th>NIRF Ranking 2025</th></tr>
<tr><td>{College_Engineering_Name_1}</td><td>{Fees_Engineering_Range_1}</td><td>{Latest_Engineering_NIRF_Rank_1}</td></tr>
</table>

<h2>Top Commerce Colleges in India</h2>
<table>
<tr><th>College Name</th><th>Fees</th><th>NIRF Ranking 2025</th></tr>
<tr><td>{College_Commerce_Name_1}</td><td>{Fees_Commerce_Range_1}</td><td>{Latest_Commerce_NIRF_Rank_1}</td></tr>
</table>
';

echo "Original template:\n";
echo $testTemplate . "\n\n";

// Simulate data - engineering has data, commerce has no data
$engineeringData = ['colleges' => [['name' => 'IIT Delhi', 'rank' => 1]]];
$commerceData = ['colleges' => []]; // No data

$engineeringReplacements = $service->formatTopCollegesForTemplate($engineeringData, 'engineering');
$commerceReplacements = $service->formatTopCollegesForTemplate($commerceData, 'commerce');

echo "Engineering replacements:\n";
print_r($engineeringReplacements);
echo "\nCommerce replacements:\n";
print_r($commerceReplacements);

// Process template to remove sections with no data
$processedTemplate = $service->removeSectionsWithNoData($testTemplate, [
    'engineering' => $engineeringReplacements,
    'commerce' => $commerceReplacements
]);

echo "\nProcessed template (commerce section should be removed):\n";
echo $processedTemplate . "\n";
