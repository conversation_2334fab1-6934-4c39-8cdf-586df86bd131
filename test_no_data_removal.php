<?php

defined('YII_DEBUG') or define('YII_DEBUG', true);
defined('YII_ENV') or define('YII_ENV', 'dev');

require __DIR__ . '/vendor/autoload.php';
require __DIR__ . '/vendor/yiisoft/yii2/Yii.php';
require __DIR__ . '/common/config/bootstrap.php';
require __DIR__ . '/console/config/bootstrap.php';

$config = yii\helpers\ArrayHelper::merge(
    require __DIR__ . '/common/config/main.php',
    require __DIR__ . '/console/config/main.php'
);

$application = new yii\console\Application($config);

$collegeService = new \common\services\CollegeService();

// Test template content with sections that might have no data
$testTemplate = '
<h2>Top Engineering Colleges in India</h2>
<table>
<tr><th>College Name</th><th>Fees</th><th>NIRF Ranking 2025</th></tr>
<tr><td>{College_Engineering_Name_1}</td><td>{Fees_Engineering_Range_1}</td><td>{Latest_Engineering_NIRF_Rank_1}</td></tr>
<tr><td>{College_Engineering_Name_2}</td><td>{Fees_Engineering_Range_2}</td><td>{Latest_Engineering_NIRF_Rank_2}</td></tr>
<tr><td>{College_Engineering_Name_3}</td><td>{Fees_Engineering_Range_3}</td><td>{Latest_Engineering_NIRF_Rank_3}</td></tr>
<tr><td>{College_Engineering_Name_4}</td><td>{Fees_Engineering_Range_4}</td><td>{Latest_Engineering_NIRF_Rank_4}</td></tr>
<tr><td>{College_Engineering_Name_5}</td><td>{Fees_Engineering_Range_5}</td><td>{Latest_Engineering_NIRF_Rank_5}</td></tr>
</table>

<h2>Top Management Colleges in India</h2>
<table>
<tr><th>College Name</th><th>Fees</th><th>NIRF Ranking 2025</th></tr>
<tr><td>{College_Management_Name_1}</td><td>{Fees_Management_Range_1}</td><td>{Latest_Management_NIRF_Rank_1}</td></tr>
<tr><td>{College_Management_Name_2}</td><td>{Fees_Management_Range_2}</td><td>{Latest_Management_NIRF_Rank_2}</td></tr>
<tr><td>{College_Management_Name_3}</td><td>{Fees_Management_Range_3}</td><td>{Latest_Management_NIRF_Rank_3}</td></tr>
<tr><td>{College_Management_Name_4}</td><td>{Fees_Management_Range_4}</td><td>{Latest_Management_NIRF_Rank_4}</td></tr>
<tr><td>{College_Management_Name_5}</td><td>{Fees_Management_Range_5}</td><td>{Latest_Management_NIRF_Rank_5}</td></tr>
</table>

<h2>Top Commerce Colleges in India</h2>
<table>
<tr><th>College Name</th><th>Fees</th><th>NIRF Ranking 2025</th></tr>
<tr><td>{College_Commerce_Name_1}</td><td>{Fees_Commerce_Range_1}</td><td>{Latest_Commerce_NIRF_Rank_1}</td></tr>
<tr><td>{College_Commerce_Name_2}</td><td>{Fees_Commerce_Range_2}</td><td>{Latest_Commerce_NIRF_Rank_2}</td></tr>
<tr><td>{College_Commerce_Name_3}</td><td>{Fees_Commerce_Range_3}</td><td>{Latest_Commerce_NIRF_Rank_3}</td></tr>
<tr><td>{College_Commerce_Name_4}</td><td>{Fees_Commerce_Range_4}</td><td>{Latest_Commerce_NIRF_Rank_4}</td></tr>
<tr><td>{College_Commerce_Name_5}</td><td>{Fees_Commerce_Range_5}</td><td>{Latest_Commerce_NIRF_Rank_5}</td></tr>
</table>
';

echo "Original template:\n";
echo $testTemplate . "\n\n";

// Test the processAllListingTemplate method
echo "Testing processAllListingTemplate method:\n";
$processed = $collegeService->processAllListingTemplate($testTemplate);
echo $processed . "\n";
